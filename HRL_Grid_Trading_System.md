# Hierarchical Reinforcement Learning (HRL) Grid Trading System

## 🧠 Architecture
- **Hierarchical Reinforcement Learning (HRL)**
- **LSTM-based low-level policy**
- **Two-layer agent structure:**
  - **Low-Level Agent** → Executes tactical trades on a grid system.
  - **High-Level Agent** → Chooses strategic parameters and evaluates overall performance.

---

## 🎯 Action Space (3 Actions)
1. **Buy at Grid** → Enter long at the current grid level, exit at **1 grid level above**.
2. **Sell at Grid** → Enter short at the current grid level, exit at **1 grid level below**.
3. **Do Nothing** → No trade.

---

## 📏 Grid Specifications
- **Grid Spacing:** 0.25% of price.
- **Risk-to-Reward Ratio:** 2.5:1.
- **Profit per Win:** $10.
- **Loss per Loss:** $4.

---

## 🔻 Low-Level Agent (Execution Policy)
- **Optimizes:**
  - **Win Rate** (40%)
  - **Trades per Day** (30%)
  - **Composite Tactical Score** (30%) — blends reward size, volatility control, and execution efficiency.

**Constraints:**
- Trades only at defined grid levels.
- Exits strictly at +1 grid level (buy) or −1 grid level (sell) from entry.

---

## 🔺 High-Level Agent (Strategic Policy)
- **Optimizes Composite Metric:**

| Metric                     | Weight |
|----------------------------|--------|
| SQN                        | 20%    |
| Calmar Ratio               | 20%    |
| Sortino Ratio              | 15%    |
| Profit Factor              | 10%    |
| Equity Curve Smoothness    | 15%    |
| Win Rate (%)               | 10%    |
| Generalization Indicator   | 10%    |
| **Total**                  | **100%** |

**Role:** Adjusts strategic rules, e.g., deciding how often to act, market regimes, volatility filters.

---

## 📊 Data
- **Market:** BTC/USDT
- **Timeframe:** 4-hour candles
- **History:** 4 years
  - **Training:** 2 years
  - **Out-of-sample Testing:** 1 year
  - **Backtesting:** 1 year

---

## ✅ Consistency Check
- **Actions**: Updated to grid-specific logic ✅
- **RR values**: 2.5:1 matches $10 win / $4 loss ✅
- **Metrics**: Still compatible with grid trading ✅
- **Constraints**: Enforced grid-level execution ✅
